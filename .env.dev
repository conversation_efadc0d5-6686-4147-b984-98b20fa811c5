

# Google Cloud Service Account Credentials
# For local development - choose ONE of the following:
# Option 1: Application Default Credentials (recommended)
# Run: gcloud auth application-default login
# Option 2: Service Account Key Files
GOOGLE_APPLICATION_CREDENTIALS=credentials/konveyn2ai-vertex-ai-key.json
# GOOGLE_APPLICATION_CREDENTIALS=credentials/konveyn2ai-cloud-run-key.json

# Google Gemini API  
GOOGLE_GEMINI_API_KEY=your_gemini_api_key_here

# Project Configuration
PROJECT_ID=konveyn2ai
REGION=us-central1
VECTOR_INDEX_ID=805460437066842112

# Component Service URLs (for inter-service communication)
AMATYA_SERVICE_URL=https://amatya-role-prompter-***********.us-central1.run.app
JANAPADA_SERVICE_URL=https://janapada-memory-***********.us-central1.run.app
SVAMI_SERVICE_URL=https://svami-orchestrator-***********.us-central1.run.app

# Development Environment
ENVIRONMENT=development

# Google Cloud Configuration
GOOGLE_API_KEY=AIzaSyAjzGWbzirHWvLh_JZnfMXB8wVB3pCTsi0

# Google Gemini API
GOOGLE_GEMINI_API_KEY=AIzaSyAjzGWbzirHWvLh_JZnfMXB8wVB3pCTsi0