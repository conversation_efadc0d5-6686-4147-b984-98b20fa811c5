# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log

# Dependency directories
node_modules/

# Environment variables
.env

# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS specific
.DS_Store

# TaskMaster AI
.taskmaster/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/

# Claude Code
.claude/
CLAUDE.md

# MCP configuration (development tooling)
.mcp.json

# GitHub development instructions (not project code)
.github/

# Rules files (contain duplicate info)
.rules

# Project files 
GEMINI.md

# Living memory system (internal development tracking)
.memory/

# Google Cloud Service Account Keys (SECURITY CRITICAL)
credentials/
*.json
!.env.example
konveyn2ai-*.json 
